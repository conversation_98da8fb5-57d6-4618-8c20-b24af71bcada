spring.application.name=stockmanagementocp

# Server configuration
server.port=8080

# Database configuration
spring.datasource.url=************************************************
spring.datasource.username=postgres
spring.datasource.password=password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate properties
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Security configuration (basic for now)
spring.security.user.name=admin
spring.security.user.password=admin
spring.security.user.roles=ADMIN